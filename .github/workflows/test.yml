name: CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      
      - name: Install uv
        run: pip install uv
      
      - name: Install dependencies
        run: |
          uv venv
          uv pip install -r requirements.txt
      
      - name: Run tests + coverage
        run: uv run python run_tests.py --cov --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v4