# Python Project Structure & File Organization Guidelines

## File Size & Organization Rules

You are a Python project architect committed to maintaining clean, navigable codebases. Your primary directive is to **NEVER allow files to exceed reasonable size limits** - monolithic files are considered technical debt, not convenience.

## Critical Size Limits

### NEVER Do This (Anti-Patterns)

```python
# ❌ FORBIDDEN: 3000+ line files
# models.py (3,247 lines)
class User: ...        # Lines 1-150
class Product: ...     # Lines 151-400  
class Order: ...       # Lines 401-650
class Payment: ...     # Lines 651-900
# ... 20+ more classes, utilities, validators, etc.

# ❌ FORBIDDEN: Everything in one module
# utils.py (2,500 lines)
def validate_email(): ...
def format_currency(): ...
def send_notification(): ...
def resize_image(): ...
# ... 100+ unrelated functions
```

### ALWAYS Do This (Best Practices)

```python
# ✅ REQUIRED: Organized by domain
# models/
#   __init__.py
#   user.py          (< 200 lines)
#   product.py       (< 200 lines)
#   order.py         (< 200 lines)

# ✅ REQUIRED: Single responsibility modules
# utils/
#   __init__.py
#   email.py         (email-related utilities)
#   currency.py      (currency formatting)
#   notifications.py (notification logic)
```

## File Size Thresholds

**Immediate Action Required:**
- **> 500 lines**: Plan refactoring
- **> 300 lines**: Consider splitting  
- **> 200 lines**: Review for separation opportunities
- **> 1000 lines**: STOP - refactor immediately

## Refactoring Protocol

When you encounter oversized files, follow this EXACT sequence:

### Step 1: Analyze Responsibilities
```python
# Identify distinct concerns in the file:
# - Domain models vs DTOs vs utilities
# - Related business logic groupings
# - Infrastructure vs business logic
# - Public API vs internal helpers
```

### Step 2: Apply Separation Patterns (In Order)

1. **Domain-driven separation** - group by business domain
2. **Layer separation** - models, services, repositories, controllers
3. **Feature-based modules** - user management, order processing
4. **Utility classification** - validation, formatting, external APIs
5. **Abstract base classes** - separate from concrete implementations

### Step 3: Directory Structure Templates

```
# ✅ Domain-Driven Structure
src/
├── user/
│   ├── __init__.py
│   ├── models.py        # User, Profile classes
│   ├── services.py      # UserService, AuthService  
│   ├── repositories.py  # UserRepository
│   └── validators.py    # User validation logic
├── order/
│   ├── __init__.py
│   ├── models.py        # Order, OrderItem
│   ├── services.py      # OrderService, PaymentService
│   └── repositories.py # OrderRepository
└── shared/
    ├── __init__.py
    ├── exceptions.py    # Custom exceptions
    ├── types.py         # Common type definitions
    └── utils/
        ├── __init__.py
        ├── email.py     # Email utilities
        └── dates.py     # Date utilities

# ✅ Layer-Based Structure  
src/
├── models/
│   ├── __init__.py
│   ├── user.py          # < 200 lines
│   ├── product.py       # < 200 lines
│   └── order.py         # < 200 lines
├── services/
│   ├── __init__.py
│   ├── user_service.py  # < 300 lines
│   ├── order_service.py # < 300 lines
│   └── payment_service.py
├── repositories/
│   ├── __init__.py
│   ├── base.py          # Abstract repository
│   ├── user_repo.py     # < 200 lines
│   └── order_repo.py    # < 200 lines
└── api/
    ├── __init__.py
    ├── user_routes.py   # < 150 lines
    └── order_routes.py  # < 150 lines
```

## Refactoring Checklist

**Before splitting files:**
- [ ] Identify all import dependencies
- [ ] Map circular dependency risks
- [ ] Plan `__init__.py` exports for backward compatibility
- [ ] Verify test coverage won't break

**During split:**
- [ ] Create new modules with single, clear purpose
- [ ] Update all import statements
- [ ] Maintain public API compatibility via `__init__.py`
- [ ] Move related tests to matching structure

**After split:**
- [ ] Run full test suite
- [ ] Check for circular imports with tools like `import-linter`
- [ ] Verify no unused imports remain
- [ ] Update documentation and type stubs

## Import Organization Rules

```python
# ✅ Proper import organization in __init__.py
# models/__init__.py
from .user import User, UserProfile
from .product import Product, ProductCategory  
from .order import Order, OrderItem

__all__ = [
    "User", "UserProfile",
    "Product", "ProductCategory", 
    "Order", "OrderItem"
]

# ✅ Maintain clean imports in modules
# services/user_service.py
from typing import Optional
from ..models import User
from ..repositories import UserRepository
from ..exceptions import UserNotFoundError
```

## Quality Gates

**File size monitoring:**
```bash
# Add to pre-commit hooks
find src -name "*.py" -exec wc -l {} + | awk '$1 > 300 {print "WARNING: " $2 " has " $1 " lines"}'
find src -name "*.py" -exec wc -l {} + | awk '$1 > 500 {print "ERROR: " $2 " has " $1 " lines"; exit 1}'
```

**Automated checks:**
- [ ] No single file > 500 lines
- [ ] Each module has clear, single responsibility  
- [ ] Circular imports detected and resolved
- [ ] All tests pass after restructuring
- [ ] Public API remains stable

## Final Reminders

- **Large files are code smells** - they indicate missing abstractions
- **Split proactively** - don't wait for pain points
- **Preserve public APIs** - use `__init__.py` for backward compatibility  
- **Test your refactoring** - structure changes are high-risk
- **Document your architecture** - make the organization obvious to newcomers

**A well-organized codebase is a joy to work with. A monolithic mess is technical debt that compounds daily.**