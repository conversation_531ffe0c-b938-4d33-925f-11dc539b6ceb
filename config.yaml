# Provider selection (openrouter or claude_code)
provider: "claude_code"

# Claude Code settings (when provider is "claude_code")
claude_code:
  model: "claude-sonnet-4-20250514"  # Claude Sonnet 4 as default
  max_turns: 10
  cli_path: "claude"  # Path to claude CL<PERSON> (if not in PATH)
  # system_prompt: |  # Optional, will use general system_prompt if not specified
  #   You are a helpful research assistant...

# OpenRouter API settings (when provider is "openrouter")
openrouter:
  api_key: "YOUR KEY"
  base_url: "https://openrouter.ai/api/v1"
  
  # IMPORTANT: When selecting a model, ensure it has a high context window (200k+ tokens recommended)
  # The orchestrator can generate large amounts of results from multiple agents that need to be
  # processed together during synthesis. Low context window models may fail or truncate results.
  model: "moonshotai/kimi-k2"

# System prompt for the agent
system_prompt: |
  You are a helpful research assistant. When users ask questions that require 
  current information or web search, use the search tool and all other tools available to find relevant 
  information and provide comprehensive answers based on the results.
  
  IMPORTANT: When you have fully satisfied the user's request and provided a complete answer, 
  you MUST call the mark_task_complete tool with a summary of what was accomplished and 
  a final message for the user. This signals that the task is finished.

# Agent settings
agent:
  max_iterations: 10

# Orchestrator settings
orchestrator:
  parallel_agents: 4  # Number of agents to run in parallel
  task_timeout: 300   # Timeout in seconds per agent
  aggregation_strategy: "consensus"  # How to combine results
  
  # Question generation prompt for orchestrator
  question_generation_prompt: |
    You are an orchestrator that needs to create {num_agents} different questions to thoroughly analyze this topic from multiple angles.
    
    Original user query: {user_input}
    
    Generate exactly {num_agents} different, specific questions that will help gather comprehensive information about this topic.
    Each question should approach the topic from a different angle (research, analysis, verification, alternatives, etc.).
    
    Return your response as a JSON array of strings, like this:
    ["question 1", "question 2", "question 3", "question 4"]
    
    Only return the JSON array, nothing else.

  # Synthesis prompt for combining all agent responses
  synthesis_prompt: |
    You have {num_responses} different AI agents that analyzed the same query from different perspectives. 
    Your job is to synthesize their responses into ONE comprehensive final answer.
    
    Here are all the agent responses:
    
    {agent_responses}
    
    IMPORTANT: Just synthesize these into ONE final comprehensive answer that combines the best information from all agents. 
    Do NOT call mark_task_complete or any other tools. Do NOT mention that you are synthesizing multiple responses. 
    Simply provide the final synthesized answer directly as your response.

# Search tool settings
search:
  max_results: 5
  user_agent: "Mozilla/5.0 (compatible; OpenRouter Agent)"

# Timeout settings (in seconds)
timeouts:
  cli_verification: 5
  progress_update_interval: 5
  subprocess_timeout: 120
  tool_execution: 30

# Display and formatting limits
display:
  preview_lines: 10
  preview_display_lines: 5
  line_truncate_length: 80
  json_preview_length: 50
  max_prompt_size: 10000  # Maximum characters for system prompt
  
# Performance settings
performance:
  cache_tool_discovery: true
  cache_system_prompts: true
  lazy_load_tool_examples: false  # Set to true for large tool sets

# ============================================================================
# AGENT AND ORCHESTRATOR CUSTOMIZATION (Optional)
# ============================================================================
# The following sections show how to configure individual agents and 
# orchestrator with different models and prompts. These are optional - 
# if not specified, all agents will use the global configuration above.

# Agent-specific configurations (optional)
# Uncomment and modify to use different models/prompts for individual agents
# agents:
#   # Configuration for agent 1
#   agent_1:
#     provider: "openrouter"  # Override global provider
#     model: "anthropic/claude-3.5-sonnet"  # Use a different model
#     system_prompt: |
#       You are a specialized research agent focused on finding comprehensive 
#       information and current data. Use all available tools to gather facts.
#   
#   # Configuration for agent 2  
#   agent_2:
#     # Uses global provider (claude_code) but different model
#     model: "claude-opus-4-20250514"  # Premium model for complex analysis
#     system_prompt: |
#       You are an analysis expert focused on critical evaluation and 
#       identifying potential issues, risks, or contradictions.
#   
#   # Configuration for agent 3
#   agent_3:
#     provider: "openrouter"
#     model: "openai/gpt-4.1-mini"  # Fast model for quick tasks
#     system_prompt: |
#       You are focused on practical solutions and actionable recommendations.
#   
#   # Configuration for agent 4
#   agent_4:
#     model: "claude-sonnet-4-20250514"  # Same as global but custom prompt
#     system_prompt: |
#       You are focused on alternative perspectives and creative solutions.

# Orchestrator model customization (optional)
# Uncomment to use a different model for orchestrator operations
# orchestrator:
#   # ... existing orchestrator settings remain ...
#   
#   # Optional: Use a different provider/model for orchestrator
#   provider: "openrouter"  # Override global provider for orchestrator
#   model: "anthropic/claude-3.5-sonnet"  # High-quality model for synthesis
#   
#   # Or use same provider but different model:
#   # model: "claude-opus-4-20250514"  # Uses global provider

# ============================================================================
# EXAMPLE CONFIGURATIONS
# ============================================================================

# Example 1: Mixed providers with specialized agents
# ---------------------------------------------------
# provider: "claude_code"
# 
# agents:
#   agent_1:
#     provider: "openrouter"
#     model: "anthropic/claude-3.5-sonnet"
#     api_key: "YOUR_OPENROUTER_KEY"  # Can override API key per agent
#   agent_2:
#     model: "claude-opus-4-20250514"  # Claude Code provider
#   agent_3:
#     provider: "openrouter" 
#     model: "google/gemini-2.0-flash-001"
#   agent_4:
#     model: "claude-sonnet-4-20250514"  # Claude Code provider
# 
# orchestrator:
#   provider: "openrouter"
#   model: "openai/gpt-4.1-mini"  # Fast synthesis

# Example 2: All OpenRouter with model specialization
# ---------------------------------------------------
# provider: "openrouter"
# 
# agents:
#   agent_1:
#     model: "anthropic/claude-3.5-sonnet"  # Deep analysis
#   agent_2:
#     model: "openai/gpt-4.1-mini"  # Quick responses
#   agent_3:
#     model: "meta-llama/llama-3.1-70b"  # Open source option
#   agent_4:
#     model: "google/gemini-2.0-flash-001"  # Multimodal capable
# 
# orchestrator:
#   model: "anthropic/claude-3.5-sonnet"  # Best for synthesis